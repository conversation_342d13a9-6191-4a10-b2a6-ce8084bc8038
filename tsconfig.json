{"compilerOptions": {"esModuleInterop": true, "module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "es2017", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "paths": {"@common/*": ["src/common/*"], "@orchestrator/*": ["src/orchestrator/*"], "@intelligence/*": ["src/intelligence/*"], "@business-base/*": ["src/business-base/*"], "@auth/*": ["src/auth/*"], "@message-hub/*": ["src/message-hub/*"], "@data-insights/*": ["src/data-insights/*"], "@app.module": ["src/app.module"]}}, "include": ["src/**/*", "test/**/*", "scripts/**/*"], "exclude": ["node_modules", "dist", "build", ".docker"]}