import { Inject, Injectable } from '@nestjs/common';
import { randomUUID as uuidv4 } from 'crypto';
import { logger } from '@edutalent/commons-sdk';
import {
  BusinessException,
  BusinessExceptionStatus,
} from '@common/exception/types/BusinessException';
import { PortfolioItemPort } from '@business-base/infrastructure/ports/db/portfolio-item.port';
import { ResponsePortfolioItemDto } from '@business-base/application/dto/out/response-portfolio-item.dto';
import { PortfolioItemDto } from '@business-base/application/dto/in/portfolio-item.dto';
import { PortfolioItemEntity } from '@business-base/domain/entities/portfolio-item.entity';
import {
  FollowUpType,
  GroupByDate,
  MessageType,
  PortfolioExecutionStatus,
  PortfolioItemStatus,
  RecordStatus,
  RoleType,
} from '@common/enums';
import { PortfolioItemCustomDataPort } from '@business-base/infrastructure/ports/db/portfolio-item-custom-data.port';
import { PortfolioItemExecutionHistoryUseCase } from '@business-base/application/use-cases/portfolio-item-execution-history.use-case';
import { MessageHistoryResponseDto } from '@business-base/misc/interfaces/in/message-history-response.dto';
import { InfraConversationHistoryPort } from '@business-base/infrastructure/ports/http/conversation-history.port';
import { PortfolioItemWorkflowExecutionPort } from '@business-base/infrastructure/ports/db/portfolio-item-worflow-execution.port';
import { PaginatedDto } from '@common/pagination/paginated';
import { isNotEmpty } from 'class-validator';
import { ExecutePortfolioItemDto } from '@business-base/application/dto/in/execute-portfolio-item.dto';
import { CustomerPort } from '@business-base/infrastructure/ports/db/customer.port';
import { PortfolioPort } from '@business-base/infrastructure/ports/db/portfolio.port';
import { SQSService } from '@common/sqs/sqs.service';
import { InfraMessageHubPort } from '@business-base/infrastructure/ports/http/message-hub.port';
import { CustomerEntity } from '@business-base/domain/entities/customer.entity';
import { SendDirectMessageDto } from '@business-base/application/dto/out/send-direct-message.dto';
import { PortfolioEntity } from '@business-base/domain/entities/portfolio.entity';
import { MiddlewareResponseOutputPort } from '@business-base/infrastructure/ports/db/middleware-response-output.port';
import { S3Service } from '@common/s3/s3.service';
import { Readable } from 'node:stream';
import { MessageHubOutgoingMessagePort } from '@business-base/infrastructure/ports/db/message-hub-outgoing-message.port';
import { PortfolioItemScheduledFollowUpPort } from '@business-base/infrastructure/ports/db/portfolio-item-scheduled-follow-up.port';
import { PortfolioItemScheduledFollowUpEntity } from '@business-base/domain/entities/portfolio-item-scheduled-follow-up.entity';
import { CustomerPreferencesUseCase } from '@business-base/application/use-cases/customer-preferences.use-case';
import { CorrelationContextService } from '@common/services/correlation-context.service';
import { CustomerPreferencesPort } from '@business-base/infrastructure/ports/db/customer-preferences.port';
import { StatisticalDataUseCase } from '@business-base/application/use-cases/statistical-data.use-case';

@Injectable()
export class PortfolioItemUseCase {
  private readonly directMessageFilesBucketName;

  constructor(
    @Inject('PortfolioItemPort')
    private readonly portfolioItemAdapter: PortfolioItemPort,
    @Inject('PortfolioItemCustomDataPort')
    private readonly portfolioItemCustomDataAdapter: PortfolioItemCustomDataPort,
    @Inject('MiddlewareResponseOutputPort')
    private readonly middlewareResponseOutputAdapter: MiddlewareResponseOutputPort,
    private readonly portfolioItemExecutionHistoryUseCase: PortfolioItemExecutionHistoryUseCase,
    @Inject('InfraConversationHistoryPort')
    private readonly infraConversationHistoryAdapter: InfraConversationHistoryPort,
    @Inject('PortfolioItemWorkflowExecutionPort')
    private readonly portfolioItemWorkflowExecutionAdapter: PortfolioItemWorkflowExecutionPort,
    @Inject('PortfolioItemScheduledFollowUpPort')
    private readonly portfolioItemScheduledFolloupAdapter: PortfolioItemScheduledFollowUpPort,
    @Inject('CustomerPort')
    private readonly customerAdapter: CustomerPort,
    @Inject('PortfolioPort')
    private readonly portfolioAdapter: PortfolioPort,
    @Inject('InfraMessageHubPort')
    private readonly messageHubAdapter: InfraMessageHubPort,
    @Inject('MessageHubOutgoingMessagePort')
    private readonly messageHubOutgoingMessageAdapter: MessageHubOutgoingMessagePort,
    private readonly sqsService: SQSService,
    private readonly s3Service: S3Service,
    private readonly customerPreferencesUseCase: CustomerPreferencesUseCase,
    @Inject('CustomerPreferencesPort')
    private readonly customerPreferencesAdapter: CustomerPreferencesPort,
    private readonly statisticalDataUseCase: StatisticalDataUseCase,
  ) {
    this.directMessageFilesBucketName = process.env.DIRECT_MESSAGE_FILES_BUCKET;
  }

  async create(createPortfolioItemDto: PortfolioItemDto): Promise<ResponsePortfolioItemDto> {
    logger.info(`Creating portfolio item: ${JSON.stringify(createPortfolioItemDto)}`);

    logger.info(`Creating portfolioItem with portfolioId: ${createPortfolioItemDto.portfolioId}`);
    createPortfolioItemDto.id = uuidv4();
    const protocolNumber = `${new Date().getFullYear()}${Math.floor(
      new Date().getTime() / 1000,
    )}${Math.random().toString(36).substring(2, 8)}`;

    const customDataId = (
      await this.portfolioItemCustomDataAdapter.create({
        id: uuidv4(),
        portfolioItemId: createPortfolioItemDto.id,
        customData: {
          ...createPortfolioItemDto.customData,
          PROTOCOL_NUMBER: protocolNumber,
        },
      })
    ).id;

    const portfolioItem = await this.portfolioItemAdapter.create(
      this.createPortfolioItemEntity(createPortfolioItemDto, customDataId),
    );

    return this.createResponsePortfolioItemDto(portfolioItem);
  }

  async findAll(
    searchOptions: any,
    customerId: string,
    page: number,
    limit: number,
    sort: string,
  ): Promise<PaginatedDto<ResponsePortfolioItemDto>> {
    logger.info('Finding all portfolioItems');
    const sortObject = this.parseSortString(sort);
    const customerPortfolios = await this.portfolioAdapter.getAll({ customerId });
    const customerPortfoliosIds = customerPortfolios.map(portfolio => portfolio.id);
    const combinedSearchOptions = { portfolioId: { in: customerPortfoliosIds }, ...searchOptions };
    const portfolioItemsPaginated = await this.portfolioItemAdapter.findManyPaginated(
      { where: { ...combinedSearchOptions } },
      {
        page: page || 1,
        limit: limit || 10,
        sort: sortObject,
      },
    );

    const { data, ...rest } = portfolioItemsPaginated;

    return {
      items: await Promise.all(
        data.map(async item => await this.enrichPortfolioItemWithMiddlewareResponseOutput(item)),
      ),
      ...rest,
    };
  }

  async findById(portfolioItemId: string): Promise<ResponsePortfolioItemDto> {
    logger.info(`Finding portfolioItem with id: ${portfolioItemId}`);

    const portfolioItem = await this.portfolioItemAdapter.get(portfolioItemId);

    if (!portfolioItem) {
      logger.error(`Portfolio item not found: ${portfolioItemId}`);
      throw new BusinessException(
        'Portfolio-item-use-case',
        `PortfolioItem with id ${portfolioItemId} not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    const portfolioItemCustomData = await this.portfolioItemCustomDataAdapter.getByPortfolioItemId(
      portfolioItem.customDataId,
      portfolioItem.id,
    );

    const portfolioItemMiddlewareResponseOutput = portfolioItem.middlewareResponseOutputId
      ? await this.middlewareResponseOutputAdapter.getByPortfolioItemId(
          portfolioItem.middlewareResponseOutputId,
          portfolioItem.id,
        )
      : null;

    const filteredMiddlewareResponseOutput = portfolioItemMiddlewareResponseOutput
      ? this.filterShowOffData(portfolioItemMiddlewareResponseOutput.data)
      : {};

    const portfolioItemDto = this.createResponsePortfolioItemDto(
      portfolioItem,
      portfolioItemCustomData.customData,
      filteredMiddlewareResponseOutput,
    );

    logger.info(
      `Finding portfolioItem id: ${portfolioItemId}. Response: ${JSON.stringify(portfolioItemDto)}`,
    );

    return portfolioItemDto;
  }

  async updateMiddlewareResponseOutputId(
    portfolioItemId: string,
    middlewareResponseOutputId: string,
  ): Promise<void> {
    logger.info(`Updating middleware response output for item with id: ${portfolioItemId}`);
    const portfolioItem = await this.portfolioItemAdapter.get(portfolioItemId);

    if (!portfolioItem) {
      throw new BusinessException(
        'Portfolio-item-use-case',
        `PortfolioItem with id ${portfolioItemId} not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    await this.portfolioItemAdapter.update({ ...portfolioItem, middlewareResponseOutputId });
  }

  async delete(portfolioItemId: string): Promise<ResponsePortfolioItemDto> {
    logger.info(`Deleting portfolioItem with id: ${portfolioItemId}`);
    const portfolioItem = await this.portfolioItemAdapter.get(portfolioItemId);

    if (!portfolioItem) {
      throw new BusinessException(
        'Portfolio-item-use-case',
        `PortfolioItem with id ${portfolioItemId} not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    portfolioItem.currentStatus = PortfolioItemStatus.CANCELLED;

    const updatedPortfolioItemEntity = await this.portfolioItemAdapter.update(portfolioItem);

    return this.createResponsePortfolioItemDto(updatedPortfolioItemEntity);
  }

  async updateItemCurrentStatus(
    portfolioItemId: string,
    currentStatus: PortfolioItemStatus,
    reason?: string,
  ): Promise<ResponsePortfolioItemDto> {
    logger.info(`Updating portfolioItem with id: ${portfolioItemId} to ${currentStatus}`);
    const portfolioItem = await this.getPortfolioItemEntity(portfolioItemId);

    if (portfolioItem.currentStatus === currentStatus) {
      logger.info(`PortfolioItem ${portfolioItemId} already in status ${currentStatus}.`);
      return this.createResponsePortfolioItemDto(portfolioItem);
    }

    const updatedPortfolioItem = {
      ...portfolioItem,
      currentStatus,
    };

    await this.portfolioItemAdapter.update(updatedPortfolioItem);

    await this.portfolioItemExecutionHistoryUseCase.create({
      portfolioItemId,
      oldStatus: portfolioItem.currentStatus,
      newStatus: updatedPortfolioItem.currentStatus,
      reason: reason,
    });

    await this.updateWaitingBusinessUserResponse(portfolioItemId, false);

    return this.createResponsePortfolioItemDto(updatedPortfolioItem);
  }

  async updateLastInteraction(portfolioItemId: string): Promise<void> {
    const lastInteraction = new Date();
    logger.info(
      `Updating last interaction for portfolioItem with id: ${portfolioItemId} to: ${lastInteraction}`,
    );
    const portfolioItem = await this.portfolioItemAdapter.get(portfolioItemId);

    if (!portfolioItem) {
      throw new BusinessException(
        'Portfolio-item-use-case',
        `PortfolioItem with id ${portfolioItemId} not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    await this.portfolioItemAdapter.update({
      ...portfolioItem,
      lastInteraction,
    });
  }

  async updateLastMessageSentAt(portfolioItemId: string): Promise<void> {
    const lastMessageSentAt = new Date();
    logger.info(
      `Updating last interaction for portfolioItem with id: ${portfolioItemId} to: ${lastMessageSentAt}`,
    );
    const portfolioItem = await this.portfolioItemAdapter.get(portfolioItemId);

    if (!portfolioItem) {
      throw new BusinessException(
        'Portfolio-item-use-case',
        `PortfolioItem with id ${portfolioItemId} not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    await this.portfolioItemAdapter.update({
      ...portfolioItem,
      lastMessageSentAt,
    });
  }

  async updateWaitingBusinessUserResponse(
    portfolioItemId: string,
    waitingBusinessUserResponse: boolean,
  ): Promise<void> {
    const portfolioItem = await this.portfolioItemAdapter.get(portfolioItemId);

    if (!portfolioItem) {
      throw new BusinessException(
        'Portfolio-item-use-case',
        `PortfolioItem with id ${portfolioItemId} not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    logger.info(
      `SettingWaitingBusinessUserResponse - update waitingBusinessUserResponse for portfolioItem with id: ${portfolioItemId} to: ${waitingBusinessUserResponse}`,
    );

    await this.portfolioItemAdapter.update({
      ...portfolioItem,
      waitingBusinessUserResponse,
    });
  }

  async findConversationHistoryById(portfolioItemId: string): Promise<MessageHistoryResponseDto[]> {
    logger.info(`Finding conversation history for portfolioItem with id: ${portfolioItemId}`);
    const portfolioItem = await this.portfolioItemAdapter.get(portfolioItemId);

    if (!portfolioItem) {
      throw new BusinessException(
        'Portfolio-item-use-case',
        `PortfolioItem with id ${portfolioItemId} not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    const portfolio = await this.portfolioAdapter.get(portfolioItem.portfolioId);
    if (!portfolio) {
      throw new BusinessException(
        'Portfolio-item-use-case',
        `Portfolio not found for portfolioItem ${portfolioItemId}`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    const workflowExecutionIds = (
      await this.portfolioItemWorkflowExecutionAdapter.getAll({ portfolioItemId: portfolioItemId })
    ).map(workflowExecutionId => workflowExecutionId.workflowExecutionId);

    const conversationHistoriesMap: MessageHistoryResponseDto[] = [];
    const customerId = portfolio.customerId;
    const to = portfolioItem.phoneNumber;

    await Promise.all(
      workflowExecutionIds.map(async workflowExecutionId => {
        const [conversationHistory, messageStatuses] = await Promise.all([
          this.infraConversationHistoryAdapter.retrieveConversationHistoryByWorkflowExecutionId(
            workflowExecutionId,
          ),
          this.messageHubOutgoingMessageAdapter.getOutgoingMessagesByCustomerId(customerId, to),
        ]);

        if (conversationHistory?.length > 0) {
          const enrichedHistory = conversationHistory.map(message => {
            const matchingStatus = messageStatuses.find(
              status => status.message === message.messageText,
            );
            return {
              ...message,
              sent: matchingStatus?.sent !== undefined ? matchingStatus.sent : undefined,
              sent_at: matchingStatus?.sent_at !== undefined ? matchingStatus.sent_at : undefined,
              time_to_go:
                matchingStatus?.time_to_go !== undefined
                  ? matchingStatus.time_to_go
                  : message.time_to_go,
            };
          });
          conversationHistoriesMap.push(...enrichedHistory);
        }
      }),
    );

    return conversationHistoriesMap;
  }

  async findAllItemsGroupedByStatus(
    portfolioId: string,
  ): Promise<Record<PortfolioItemStatus, number>> {
    logger.info(
      `Finding all portfolioItems grouped by status for portfolio with id: ${portfolioId}`,
    );

    return await this.portfolioItemAdapter.findAllGroupedByStatus(portfolioId);
  }

  async executeItem(executePortfolioItemDto: ExecutePortfolioItemDto): Promise<void> {
    const traceId = CorrelationContextService.getTraceId();
    logger.info(
      `Executing portfolio item to send answer message: ${JSON.stringify(executePortfolioItemDto)}`,
      { traceId },
    );

    let audioFileUrl: string | void;
    const filteredAudioUrls = executePortfolioItemDto.filesUrl
      .filter(Boolean)
      .filter(url => url.toLowerCase().endsWith('.mp3'));
    if (filteredAudioUrls && filteredAudioUrls.length > 0) {
      if (filteredAudioUrls.length == 1) {
        audioFileUrl = filteredAudioUrls[0];
      } else {
        audioFileUrl = await this.s3Service.concatAndUploadFilesStream(
          filteredAudioUrls,
          this.directMessageFilesBucketName,
          `${uuidv4()}.mp3`,
        );
      }
    }

    const audioOrTextMessageType =
      audioFileUrl && audioFileUrl.length > 0 ? MessageType.AUDIO : MessageType.TEXT;
    const imageMessagesUrls = executePortfolioItemDto.filesUrl.filter(
      url => url.toLowerCase().endsWith('.jpg') || url.toLowerCase().endsWith('.jpeg'),
    );
    const pdfMessages = executePortfolioItemDto.filesUrl.filter(url =>
      url.toLowerCase().endsWith('.pdf'),
    );

    const customerPhone =
      await this.messageHubAdapter.getCustomerPhoneByPhoneNumberAndCommunicationChannel(
        executePortfolioItemDto.to,
        executePortfolioItemDto.channel,
      );

    if (!customerPhone) {
      throw new BusinessException(
        'Portfolio-item-use-case: executeItem',
        `Customer with phone ${executePortfolioItemDto.to} not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    logger.info(`Customer phone found: ${JSON.stringify(customerPhone)}`, { traceId });

    const customer = await this.customerAdapter.get(customerPhone.customerId);
    logger.info(`Customer found: ${JSON.stringify(customer)}`, { traceId });

    if (!customer) {
      throw new BusinessException(
        'Portfolio-item-use-case: executeItem',
        `Customer with phone ${executePortfolioItemDto.to} not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    const customerPortfolios = await this.portfolioAdapter.findAllExecutingByCustomerId(
      customer.id,
    );
    logger.info(`Customer portfolios found: ${JSON.stringify(customerPortfolios)}`, { traceId });

    if (customerPortfolios.length === 0) {
      throw new BusinessException(
        'Portfolio-item-use-case: executeItem',
        `Customer with phone ${executePortfolioItemDto.to} has no active portfolios`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    const portfoliosIds = customerPortfolios.map(ports => ports.id);

    logger.info(`Portfolios ids found: ${portfoliosIds}`, { traceId });

    const portfolioItems = await this.portfolioItemAdapter.findToAnswerByPortfolioIdAndPhoneNumber(
      portfoliosIds,
      executePortfolioItemDto.from,
    );

    logger.info(`Portfolio items found: ${JSON.stringify(portfolioItems)}`, { traceId });
    //TODO: add IMAGE AND PDF treatment for DEFAULT portfolios
    if (portfolioItems.length === 0) {
      const defaultPortfolio = await this.portfolioAdapter.findDefaultByCustomerId(customer.id);
      if (!defaultPortfolio) {
        logger.info(
          `Discarding message From: ${executePortfolioItemDto.from}: ${executePortfolioItemDto.message}, No default portfolio found for customer with id: ${customer.id}.`,
          { traceId },
        );
        return;
      }

      const newPortfolioItemDto: PortfolioItemDto = {
        id: uuidv4(),
        portfolioId: defaultPortfolio.id,
        phoneNumber: executePortfolioItemDto.from,
        customData: {
          phoneNumber: executePortfolioItemDto.from,
          channel: executePortfolioItemDto.channel,
          firstMessage: executePortfolioItemDto.message,
          fileUrl: audioFileUrl,
          startDate: new Date(),
          fellIntoNet: true,
        },
        line: defaultPortfolio.totalQuantity + 1,
      };

      const newPortfolioItem = await this.create(newPortfolioItemDto);
      await this.portfolioAdapter.updateTotalQuantity(
        defaultPortfolio.id,
        defaultPortfolio.totalQuantity + 1,
      );
      await this.executeItemInbound(newPortfolioItem, customer, audioOrTextMessageType, audioFileUrl);
      return;
    }

    const portfolioItemPortfoliosMap = new Map(
      portfolioItems.map(portfolioItem => {
        const portfolio = customerPortfolios.find(
          portfolio => portfolio.id === portfolioItem.portfolioId,
        );
        return [portfolioItem, portfolio];
      }),
    );

    const [selectedPortfolioItem, selectedPortfolio] = await this.selectItemToAnswer(
      portfolioItemPortfoliosMap,
    );

    if (!selectedPortfolioItem) {
      logger.info(
        `Discarding message from ${executePortfolioItemDto.from} to ${executePortfolioItemDto.to}, message: ${executePortfolioItemDto.message}. Item was not found to answer.`,
        { traceId },
      );
      return;
    }

    await this.updateLastInteraction(selectedPortfolioItem.id);

    const portfolioItem = this.createResponsePortfolioItemDto(selectedPortfolioItem);

    //TODO: add IMAGE AND PDF treatment for inbound item execution
    if (
      portfolioItem.currentStatus === PortfolioItemStatus.PENDING &&
      selectedPortfolio.executionStatus === PortfolioExecutionStatus.INBOUND
    ) {
      await this.executeItemInbound(portfolioItem, customer, audioOrTextMessageType, audioFileUrl);
      return;
    }

    const itemWorkflowExecutions = await this.portfolioItemWorkflowExecutionAdapter.getAll({
      portfolioItemId: portfolioItem.id,
    });

    logger.info(`Item workflow executions found: ${JSON.stringify(itemWorkflowExecutions)}`, {
      traceId,
    });

    if (itemWorkflowExecutions.length === 0) {
      throw new BusinessException(
        'Portfolio-item-use-case: executeItem',
        `No workflow executions found for portfolio item ${portfolioItem.id}`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    const workflowExecutionId = itemWorkflowExecutions[0].workflowExecutionId;
    const workflowId = customerPortfolios.find(
      portfolio => portfolio.id === portfolioItem.portfolioId,
    ).workflowId;

    logger.info(`Workflow id found: ${workflowId}`, { traceId });

    if (
      portfolioItem.currentStatus === PortfolioItemStatus.UNLINKED ||
      portfolioItem.currentStatus === PortfolioItemStatus.SUCCEED ||
      portfolioItem.currentStatus === PortfolioItemStatus.FAILED ||
      portfolioItem.currentStatus === PortfolioItemStatus.OPTED_OUT ||
      portfolioItem.currentStatus === PortfolioItemStatus.FINISHED ||
      (imageMessagesUrls && imageMessagesUrls.length > 0) ||
      (pdfMessages && pdfMessages.length > 0) ||
      portfolioItem.waitingBusinessUserResponse
    ) {
      logger.info(`Message history updating: ${workflowExecutionId}`, { traceId });

      if (
        (executePortfolioItemDto.message && executePortfolioItemDto.message.length > 0) ||
        (filteredAudioUrls && filteredAudioUrls.length > 0)
      ) {
        await this.infraConversationHistoryAdapter.createConversationHistoryByWorkflowExecutionId(
          workflowExecutionId,
          new SendDirectMessageDto(
            executePortfolioItemDto.message ? executePortfolioItemDto.message : ' ',
            audioOrTextMessageType,
            RoleType.USER,
            audioFileUrl,
          ),
        );
      }

      if (imageMessagesUrls && imageMessagesUrls.length > 0) {
        for (const imageMessage of imageMessagesUrls) {
          logger.info(`Message history updating with image: ${workflowExecutionId}.`, {
            traceId,
            imageMessage,
          });
          await this.infraConversationHistoryAdapter.createConversationHistoryByWorkflowExecutionId(
            workflowExecutionId,
            new SendDirectMessageDto(
              `Image message: ${imageMessage}`,
              MessageType.JPG,
              RoleType.USER,
              imageMessage,
            ),
          );
        }
      }

      if (pdfMessages && pdfMessages.length > 0) {
        for (const pdfMessage of pdfMessages) {
          logger.info(`Message history updating with PDF: ${workflowExecutionId}.`, {
            traceId,
            pdfMessage,
          });
          await this.infraConversationHistoryAdapter.createConversationHistoryByWorkflowExecutionId(
            workflowExecutionId,
            new SendDirectMessageDto(
              `PDF message: ${pdfMessage}`,
              MessageType.PDF,
              RoleType.USER,
              pdfMessage,
            ),
          );
        }
      }

      logger.info(
        `PortfolioItem ${portfolioItem.id} will not be executed because its status ${portfolioItem.currentStatus} or it has image or pdf messages.`,
        {
          traceId,
          images: JSON.stringify(imageMessagesUrls),
          pdfs: JSON.stringify(pdfMessages),
        },
      );

      await this.updateWaitingBusinessUserResponse(portfolioItem.id, true);

      const customerPreferences = await this.customerPreferencesAdapter.getById(customer.id);
      if (customerPreferences?.portfolio?.fileReceivedDefaultAnswer &&
        (imageMessagesUrls?.length > 0 || pdfMessages?.length > 0)) {

        await this.messageHubAdapter.sendMessage({
          customerId: customer.id,
          to: portfolioItem.phoneNumber,
          messageType: MessageType.TEXT,
          communicationChannel: selectedPortfolio.communicationChannel,
          message:
          customerPreferences.portfolio.fileReceivedDefaultAnswer,
          isFirstMessage: false,
        });
      }
      return;
    }

    const message = executePortfolioItemDto.message;

    const queueUrl = this.sqsService.getQueueByTypeAndSegment('WORKFLOW', customer.segment);
    const messageBody = {
      portfolioItem,
      workflowId,
      workflowExecutionId,
      message,
      messageType: audioFileUrl ? MessageType.AUDIO : MessageType.TEXT,
      fileUrl: audioFileUrl,
      isFirstMessage: false,
    };

    await this.sqsService.produce(process.env[queueUrl], messageBody);
  }

  async executeItemInbound(
    portfolioItemDto: ResponsePortfolioItemDto,
    customer: CustomerEntity,
    messageType: MessageType,
    fileUrl: string | void,
  ): Promise<void> {
    logger.info(
      `Executing item for inbound message: ${JSON.stringify(portfolioItemDto)}. Customer id: ${
        customer.id
      }`,
    );

    const queueUrl = this.sqsService.getQueueByTypeAndSegment('WORKFLOW', customer.segment);
    const messageBody = {
      portfolioId: portfolioItemDto.portfolioId,
      portfolioItemId: portfolioItemDto.id,
      messageType,
      isFirstMessage: true,
      fileUrl,
    };

    await this.sqsService.produce(process.env[queueUrl], messageBody);
  }

  async sendDirectMessage(
    portfolioItemId: string,
    sendDirectMessageDto: SendDirectMessageDto,
    file?: Express.Multer.File,
  ): Promise<MessageHistoryResponseDto[]> {
    logger.info(`Sending direct message to portfolio item with id: ${portfolioItemId}`);
    const portfolioItem = await this.getPortfolioItemEntity(portfolioItemId);
    const portfolio = await this.portfolioAdapter.get(portfolioItem.portfolioId);
    const customer = await this.customerAdapter.get(portfolio.customerId);

    if (!portfolioItem || !portfolio || !customer) {
      throw new BusinessException(
        'Portfolio-item-use-case: sendDirectMessage',
        `Customer or Portfolio or PortfolioItem not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    const [portfolioItemWorkflowExecution] =
      await this.portfolioItemWorkflowExecutionAdapter.getAll({ portfolioItemId });

    if (!portfolioItemWorkflowExecution) {
      throw new BusinessException(
        'Portfolio-item-use-case: sendDirectMessage',
        `No workflow execution found for portfolio item ${portfolioItemId}`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    let modifiedSendDirectMessageDto = sendDirectMessageDto;
    if (file && sendDirectMessageDto.messageType === MessageType.PDF) {
      const fileRenamed = this.getFileRenamed(file);
      const s3Url = await this.s3Service.uploadFile(
        this.directMessageFilesBucketName,
        fileRenamed,
        file.buffer,
      );

      if (!s3Url) {
        throw new BusinessException(
          'Portfolio-item-use-case: sendDirectMessage',
          `Error uploading file: ${file.originalname} to S3`,
          BusinessExceptionStatus.GENERAL_ERROR,
        );
      }
      modifiedSendDirectMessageDto = { ...sendDirectMessageDto, fileUrl: s3Url };
    }

    const messagesHistory =
      await this.infraConversationHistoryAdapter.createConversationHistoryByWorkflowExecutionId(
        portfolioItemWorkflowExecution.workflowExecutionId,
        modifiedSendDirectMessageDto,
      );

    await this.messageHubAdapter.sendMessage({
      customerId: customer.id,
      to: portfolioItem.phoneNumber,
      messageType: sendDirectMessageDto.messageType,
      communicationChannel: portfolio.communicationChannel,
      message: sendDirectMessageDto.message,
      isFirstMessage: false,
      fileUrl: modifiedSendDirectMessageDto.fileUrl,
    });

    await this.updateLastMessageSentAt(portfolioItem.id);

    if (
      portfolioItem.currentStatus === PortfolioItemStatus.IN_PROGRESS &&
      sendDirectMessageDto.roleType === RoleType.ATTENDANT
    ) {
      await this.updateItemCurrentStatus(portfolioItemId, PortfolioItemStatus.UNLINKED);
    }

    await this.updateWaitingBusinessUserResponse(portfolioItemId, false);

    return messagesHistory;
  }

  async sendDirectMessageBase64(
    portfolioItemId: string,
    sendDirectMessageDto: SendDirectMessageDto,
    arquivoBase64: string,
    fileName: string,
  ): Promise<MessageHistoryResponseDto[]> {
    logger.info(`Sending direct message to portfolio item with id: ${portfolioItemId}`);
    const portfolioItem = await this.getPortfolioItemEntity(portfolioItemId);
    const portfolio = await this.portfolioAdapter.get(portfolioItem.portfolioId);
    const customer = await this.customerAdapter.get(portfolio.customerId);

    if (!portfolioItem || !portfolio || !customer) {
      throw new BusinessException(
        'Portfolio-item-use-case: sendDirectMessage',
        `Customer or Portfolio or PortfolioItem not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    const [portfolioItemWorkflowExecution] =
      await this.portfolioItemWorkflowExecutionAdapter.getAll({ portfolioItemId });

    if (!portfolioItemWorkflowExecution) {
      throw new BusinessException(
        'Portfolio-item-use-case: sendDirectMessage',
        `No workflow execution found for portfolio item ${portfolioItemId}`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    const buffer = Buffer.from(arquivoBase64, 'base64');

    let modifiedSendDirectMessageDto = sendDirectMessageDto;
    if (buffer && sendDirectMessageDto.messageType === MessageType.PDF) {
      const fileRenamed = this.getFileRenamedFromString(fileName);
      const s3Url = await this.s3Service.uploadFile(
        this.directMessageFilesBucketName,
        fileRenamed,
        buffer,
      );

      if (!s3Url) {
        throw new BusinessException(
          'Portfolio-item-use-case: sendDirectMessageBase64',
          `Error uploading file: ${fileRenamed} to S3`,
          BusinessExceptionStatus.GENERAL_ERROR,
        );
      }
      modifiedSendDirectMessageDto = { ...sendDirectMessageDto, fileUrl: s3Url };
    }

    const messagesHistory =
      await this.infraConversationHistoryAdapter.createConversationHistoryByWorkflowExecutionId(
        portfolioItemWorkflowExecution.workflowExecutionId,
        modifiedSendDirectMessageDto,
      );

    await this.messageHubAdapter.sendMessage({
      customerId: customer.id,
      to: portfolioItem.phoneNumber,
      messageType: sendDirectMessageDto.messageType,
      communicationChannel: portfolio.communicationChannel,
      message: sendDirectMessageDto.message,
      isFirstMessage: false,
      fileUrl: modifiedSendDirectMessageDto.fileUrl,
    });

    await this.updateLastMessageSentAt(portfolioItem.id);

    if (
      portfolioItem.currentStatus === PortfolioItemStatus.IN_PROGRESS &&
      sendDirectMessageDto.roleType === RoleType.USER
    ) {
      await this.updateItemCurrentStatus(portfolioItemId, PortfolioItemStatus.UNLINKED);
    }

    await this.updateWaitingBusinessUserResponse(portfolioItemId, false);

    return messagesHistory;
  }

  async getPortfolioItemsCountByDateFilteredByStatus(
    customerId: string,
    dateStart: Date,
    dateEnd: Date,
    groupByDate: GroupByDate,
    currentStatus: PortfolioItemStatus,
  ): Promise<any> {
    dateStart = new Date(dateStart);
    dateStart.setUTCHours(0, 0, 0, 0);

    dateEnd = new Date(dateEnd);
    dateEnd.setUTCHours(23, 59, 59, 999);

    const customerPortfolios = await this.portfolioAdapter.getAll({
      customerId,
      createdAt: {
        gte: dateStart.toISOString(),
        lte: dateEnd.toISOString(),
      },
    });

    const customerPortfoliosIds = customerPortfolios.map(portfolio => portfolio.id);

    const portfolioItems = await this.portfolioItemAdapter.getAll({
      portfolioId: { in: customerPortfoliosIds },
      createdAt: {
        gte: dateStart.toISOString(),
        lte: dateEnd.toISOString(),
      },
      currentStatus: currentStatus,
    });

    if (groupByDate === GroupByDate.DAY) {
      return portfolioItems.reduce((acc, item) => {
        const date = item.updatedAt.toISOString().split('T')[0];
        acc[date] = (acc[date] || 0) + 1;
        return acc;
      }, {});
    }

    if (groupByDate === GroupByDate.MONTH) {
      return portfolioItems.reduce((acc, item) => {
        const date = new Date(item.updatedAt);
        const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-01`;
        acc[monthKey] = (acc[monthKey] || 0) + 1;
        return acc;
      }, {});
    }
  }

  async getPortfolioItemsWithAiOnlyInteractionCountByDate(
    customerId: string,
    dateStart: Date,
    dateEnd: Date,
  ): Promise<number> {
    dateStart = new Date(dateStart);
    dateStart.setUTCHours(0, 0, 0, 0);

    dateEnd = new Date(dateEnd);
    dateEnd.setUTCHours(23, 59, 59, 999);

    return await this.portfolioItemAdapter.findPortfolioItemsWithAiOnlyInteractionCountByDate(
      customerId,
      dateStart,
      dateEnd,
    );
  }

  async getPortfolioItemsWithInteractionCountByDate(
    customerId: string,
    dateStart: Date,
    dateEnd: Date,
    groupByDate: GroupByDate,
  ): Promise<any> {
    dateStart = new Date(dateStart);
    dateStart.setUTCHours(0, 0, 0, 0);

    dateEnd = new Date(dateEnd);
    dateEnd.setUTCHours(23, 59, 59, 999);

    const customerPortfolios = await this.portfolioAdapter.getAll({
      customerId,
      createdAt: {
        gte: dateStart.toISOString(),
        lte: dateEnd.toISOString(),
      },
    });

    const customerPortfoliosIds = customerPortfolios.map(portfolio => portfolio.id);

    const portfolioItems = await this.portfolioItemAdapter.getAll({
      portfolioId: { in: customerPortfoliosIds },
      createdAt: {
        gte: dateStart.toISOString(),
        lte: dateEnd.toISOString(),
      },
      lastInteraction: {
        not: null,
      },
    });

    if (groupByDate === GroupByDate.DAY) {
      return portfolioItems.reduce((acc, item) => {
        const date = item.updatedAt.toISOString().split('T')[0];
        acc[date] = (acc[date] || 0) + 1;
        return acc;
      }, {});
    }

    if (groupByDate === GroupByDate.MONTH) {
      return portfolioItems.reduce((acc, item) => {
        const date = new Date(item.updatedAt);
        const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-01`;
        acc[monthKey] = (acc[monthKey] || 0) + 1;
        return acc;
      }, {});
    }
  }

  private getFileRenamed(file: Express.Multer.File) {
    return file.originalname.replace('.pdf', '') + Math.floor(new Date().getTime() / 1000) + '.pdf';
  }

  private getFileRenamedFromString(fileName: string) {
    return fileName.replace('.pdf', '') + Math.floor(new Date().getTime() / 1000) + '.pdf';
  }

  async getDirectMessageFileStream(fileKey: string): Promise<Readable> {
    const fileStream = await this.s3Service.getFileStream(
      this.directMessageFilesBucketName,
      fileKey,
    );

    if (!fileStream) {
      throw new BusinessException(
        'Portfolio-item-use-case',
        ` File: ${fileKey} not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    return fileStream;
  }

  async getDirectMessageFileStreamFromPath(filePath: string): Promise<Readable> {
    const fileStream = await this.s3Service.getFileStreamFromPath(filePath);

    if (!fileStream) {
      throw new BusinessException(
        'Portfolio-item-use-case',
        ` File: ${filePath} not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    return fileStream;
  }

  async addItemPaymentReminders(
    portfolioItemId: string,
    firstInstallmentDate: string,
    installmentCount: number,
  ) {
    try {
      const traceId = CorrelationContextService.getTraceId();
      logger.info('Creating payment reminders for portfolio item: ', {
        traceId,
        operation: 'addItemPaymentReminders',
        portfolioItemId,
        firstInstallmentDate,
        installmentCount,
        layer: 'USE_CASE',
      });

      const portfolioItem = await this.portfolioItemAdapter.get(portfolioItemId);
      const portfolio = await this.portfolioAdapter.get(portfolioItem.portfolioId);
      const customerPreferences = await this.customerPreferencesUseCase.findById(
        portfolio.customerId,
      );

      if (!customerPreferences) {
        logger.info(
          `No customer preferences found for customer with id: ${portfolio.customerId}. Using default values. For payment reminders.`,
        );
      }

      const followUpDates = this.calculateInstallmentDates(
        firstInstallmentDate,
        installmentCount,
        customerPreferences?.portfolio?.daysBeforePaymentReminder || 0,
        customerPreferences?.portfolio?.paymentReminderInDay || false,
      );
      const itemFollowUpDates = followUpDates.map(fupDate => {
        return new PortfolioItemScheduledFollowUpEntity(
          uuidv4(),
          portfolioItemId,
          portfolio.followUpWorkflowId,
          fupDate,
          null,
          false,
          FollowUpType.PAYMENT_REMINDER,
          RecordStatus.ACTIVE,
        );
      });

      const scheduledItemFollowUpDates = await this.portfolioItemScheduledFolloupAdapter.createMany(
        itemFollowUpDates,
      );

      logger.info(
        `Follow up date for portfolio item with id: ${portfolioItemId} updated successfully. Scheduled follow up date: ${JSON.stringify(
          scheduledItemFollowUpDates,
        )}`,
      );
    } catch (error) {
      logger.error(
        `Error updating payment reminder dates for portfolio item with id: ${portfolioItemId}. Dates: ${JSON.stringify(
          { firstInstallmentDate, installmentCount, error },
        )}`,
        error,
      );
    }
  }

  async clearItemFollowUpdates(portfolioItemId: string) {
    try {
      logger.info(`Clearing follow up dates for portfolio item with id: ${portfolioItemId}`);
      const portfolioItem = await this.portfolioItemAdapter.get(portfolioItemId);

      if (!portfolioItem) {
        throw new BusinessException(
          'Portfolio-item-use-case',
          `PortfolioItem with id ${portfolioItemId} not found`,
          BusinessExceptionStatus.ITEM_NOT_FOUND,
        );
      }

      await this.portfolioItemScheduledFolloupAdapter.deleteMany({ portfolioItemId });
      logger.info(`Follow up dates cleared for portfolio item with id: ${portfolioItemId}`);
    } catch (error) {
      logger.error(
        `Error clearing follow up dates for portfolio item with id: ${portfolioItemId}. Error: ${JSON.stringify(
          error,
        )}`,
        error,
      );
    }
  }

  async updateItemFollowUpDate(portfolioItemId: string, date: string) {
    try {
      logger.info(`Updating follow up dates for portfolio item with id: ${portfolioItemId}`);
      const portfolioItem = await this.portfolioItemAdapter.get(portfolioItemId);
      const portfolio = await this.portfolioAdapter.get(portfolioItem.portfolioId);

      if (!portfolioItem) {
        throw new BusinessException(
          'Portfolio-item-use-case',
          `PortfolioItem with id ${portfolioItemId} not found`,
          BusinessExceptionStatus.ITEM_NOT_FOUND,
        );
      }

      const followUpDate = new Date(date);
      const itemFollowUpDate = new PortfolioItemScheduledFollowUpEntity(
        uuidv4(),
        portfolioItemId,
        portfolio.followUpWorkflowId,
        followUpDate,
        null,
        false,
        FollowUpType.FOLLOW_UP,
        RecordStatus.ACTIVE,
      );

      const scheduledItemFollowUpDate = await this.portfolioItemScheduledFolloupAdapter.create(
        itemFollowUpDate,
      );

      logger.info(
        `Follow up date for portfolio item with id: ${portfolioItemId} updated successfully. Scheduled follow up date: ${JSON.stringify(
          scheduledItemFollowUpDate,
        )}`,
      );
    } catch (error) {
      logger.error(
        `Error updating follow up date for portfolio item with id: ${portfolioItemId}. Dates: ${JSON.stringify(
          date,
        )}`,
        error,
      );
    }
  }

  private async enrichPortfolioItemWithMiddlewareResponseOutput(
    portfolioItem: PortfolioItemEntity,
  ) {
    if (portfolioItem.middlewareResponseOutputId) {
      const middlewareResponseOutput =
        await this.middlewareResponseOutputAdapter.getByPortfolioItemId(
          portfolioItem.middlewareResponseOutputId,
          portfolioItem.id,
        );

      const filteredData = this.filterShowOffData(middlewareResponseOutput.data);

      return this.createResponsePortfolioItemDto(portfolioItem, null, filteredData);
    }

    return this.createResponsePortfolioItemDto(portfolioItem);
  }

  private createResponsePortfolioItemDto(
    portfolioItemEntity: PortfolioItemEntity,
    customData: any = null,
    middlewareResponseOutput: any = null,
  ): ResponsePortfolioItemDto {
    return new ResponsePortfolioItemDto(
      portfolioItemEntity.id,
      portfolioItemEntity.portfolioId,
      portfolioItemEntity.phoneNumber,
      portfolioItemEntity.customDataId,
      portfolioItemEntity.line,
      customData,
      portfolioItemEntity.middlewareResponseOutputId,
      middlewareResponseOutput,
      portfolioItemEntity.lastInteraction,
      portfolioItemEntity.lastMessageSentAt,
      portfolioItemEntity.lastFollowUpAt,
      portfolioItemEntity.followUpCount,
      portfolioItemEntity.waitingBusinessUserResponse,
      portfolioItemEntity.currentStatus,
      portfolioItemEntity.createdAt,
      portfolioItemEntity.updatedAt,
    );
  }

  private createPortfolioItemEntity(
    portfolioItemDto: PortfolioItemDto,
    customDataId: string,
  ): PortfolioItemEntity {
    return new PortfolioItemEntity(
      portfolioItemDto.id,
      portfolioItemDto.portfolioId,
      portfolioItemDto.phoneNumber,
      customDataId,
      portfolioItemDto.line,
      null,
      null,
      null,
      null,
      PortfolioItemStatus.PENDING,
      0,
      portfolioItemDto.createdAt,
      portfolioItemDto.updatedAt,
    );
  }

  private async getPortfolioItemEntity(portfolioItemId: string): Promise<PortfolioItemEntity> {
    const portfolioItem = await this.portfolioItemAdapter.get(portfolioItemId);

    if (!portfolioItem) {
      throw new BusinessException(
        'Portfolio-item-use-case',
        `PortfolioItem with id ${portfolioItemId} not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    return portfolioItem;
  }

  /**
   Prioridade 1: assumimos o in_progress ou followed_up
   Prioridade 2: escolher o item pending se o portfolio for execute immediatelly == false
   Prioridade 3: escolher o item com base na last intereaction mais recente
   */
  private async selectItemToAnswer(
    portfolioItemsPortfolioMap: Map<PortfolioItemEntity, PortfolioEntity>,
  ): Promise<[PortfolioItemEntity, PortfolioEntity]> {
    for (const [portfolioItem, portfolio] of portfolioItemsPortfolioMap.entries()) {
      if (
        portfolioItem.currentStatus === PortfolioItemStatus.IN_PROGRESS ||
        portfolioItem.currentStatus === PortfolioItemStatus.FOLLOWED_UP
      ) {
        return [portfolioItem, portfolio];
      } else if (
        portfolioItem.currentStatus === PortfolioItemStatus.PENDING &&
        portfolio.executionStatus === PortfolioExecutionStatus.INBOUND
      ) {
        return [portfolioItem, portfolio];
      }
    }

    if (portfolioItemsPortfolioMap.size <= 0) {
      return null;
    }

    const sortedPortfolioItems = Array.from(portfolioItemsPortfolioMap.keys()).sort(
      (a, b) => (b.lastInteraction?.getTime() || 0) - (a.lastInteraction?.getTime() || 0),
    );

    return [
      sortedPortfolioItems[sortedPortfolioItems.length - 1],
      portfolioItemsPortfolioMap.get(sortedPortfolioItems[sortedPortfolioItems.length - 1]),
    ];
  }

  private filterShowOffData(data: any): any {
    return Object.fromEntries(
      Object.entries(data)
        .filter(([_, value]) => (value as { showOff?: boolean }).showOff === true)
        .map(([key, value]) => {
          const { showOff: _showOff, ...rest } = value as { showOff?: boolean; [key: string]: any };
          return [key, rest];
        }),
    );
  }

  private parseSortString(sortString: string): Record<string, 'asc' | 'desc'>[] {
    if (sortString && isNotEmpty(sortString)) {
      try {
        return JSON.parse(sortString);
      } catch (error) {
        throw new BusinessException(
          'Pagination',
          'Invalid sort string format. Expected a format like "{lineNumber: \'asc\'}"',
          BusinessExceptionStatus.INVALID_INPUT,
        );
      }
    }
  }

  async handleStatisticValuesExtraction(portfolioItem: PortfolioItemEntity): Promise<void> {
    const traceId = CorrelationContextService.getTraceId();
    try {
      logger.info('Handling automatic statistical data extraction for SUCCEED status', {
        traceId,
        portfolioItemId: portfolioItem.id,
        portfolioId: portfolioItem.portfolioId,
        operation: 'handleStatisticValuesExtraction',
        layer: 'USE_CASE',
      });

      const portfolio = await this.portfolioAdapter.get(portfolioItem.portfolioId);

      // Get customer preferences to determine data source
      const customerPreferences = await this.customerPreferencesAdapter.getById(
        portfolio.customerId,
      );

      const statsConfig = customerPreferences?.portfolio?.statsConfig;
      if (!statsConfig || statsConfig.length === 0) {
        logger.info('No stats config found for customer', {
          traceId,
          customerId: portfolio.customerId,
          portfolioItemId: portfolioItem.id,
          operation: 'handleStatisticValuesExtraction',
          layer: 'USE_CASE',
        });
        return;
      }

      // Call the statistical data use case to extract and store all statistical data
      await this.statisticalDataUseCase.extractAndStoreStatisticalData(
        portfolio.customerId,
        portfolioItem,
        portfolio.workflowId,
        statsConfig,
      );

      logger.info('Automatic statistical data extraction completed successfully', {
        traceId,
        portfolioItemId: portfolioItem.id,
        portfolioId: portfolioItem.portfolioId,
        customerId: portfolio.customerId,
        workflowId: portfolio.workflowId,
        operation: 'handleStatisticValuesExtraction',
        layer: 'USE_CASE',
      });
    } catch (error) {
      logger.error('Error during automatic statistical data extraction', {
        traceId,
        portfolioItemId: portfolioItem.id,
        portfolioId: portfolioItem.portfolioId,
        error: JSON.stringify(error),
        operation: 'handleStatisticValuesExtraction',
        layer: 'USE_CASE',
        message: 'Statistical data extraction failed but status update will continue',
      });
    }
  }

  private calculateInstallmentDates(
    firstDueDate: string,
    numberOfInstallments: number,
    beforeDays?: number,
    inDueDateRemind: boolean = false,
  ): Date[] {
    if (numberOfInstallments < 1) {
      throw new Error('Number of installments must be at least 1');
    }

    if (beforeDays !== undefined && beforeDays < 0) {
      throw new Error('beforeDays must be a non-negative number');
    }

    const datePattern = /^(\d{2})\/(\d{2})\/(\d{4})$/;
    const match = firstDueDate.match(datePattern);

    if (!match) {
      throw new Error('Invalid date format. Use DD/MM/YYYY');
    }

    const day = parseInt(match[1], 10);
    const month = parseInt(match[2], 10) - 1; // JavaScript months are 0-based
    const year = parseInt(match[3], 10);

    const parsedDate = new Date(year, month, day);
    if (isNaN(parsedDate.getTime())) {
      throw new Error('Invalid date');
    }

    const dates: Date[] = [];
    const currentDate = new Date(parsedDate);

    for (let i = 0; i < numberOfInstallments; i++) {
      if (inDueDateRemind || beforeDays === undefined) {
        dates.push(new Date(currentDate));
      }

      if (beforeDays !== undefined) {
        const reminderDate = new Date(currentDate);
        reminderDate.setDate(currentDate.getDate() - beforeDays);
        dates.push(reminderDate);
      }

      currentDate.setMonth(currentDate.getMonth() + 1);
    }

    return dates.sort((a, b) => a.getTime() - b.getTime());
  }
}
