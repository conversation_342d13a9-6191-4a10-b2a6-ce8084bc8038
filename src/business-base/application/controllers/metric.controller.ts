import { MetricUseCase } from '@business-base/application/use-cases/metric.use-case';
import { AccountRoles } from '@common/auth/decorators/account-role.decorator';
import { UserRolesInAccount } from '@common/auth/decorators/user-role-in-account.decorator';
import { AccountRole, GroupByDate, PortfolioItemStatus, UserRoleInAccount } from '@common/enums';
import { Controller, Get, Inject, Param, Query, Req, Version } from '@nestjs/common';
import { ParseUUIDPipe } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiQuery, ApiResponse } from '@nestjs/swagger';
import { PortfolioUseCase } from '@business-base/application/use-cases/portfolio.use-case';
import { PortfolioItemPort } from '@business-base/infrastructure/ports/db/portfolio-item.port';
import { PortfolioPort } from '@business-base/infrastructure/ports/db/portfolio.port';
import { NegotiationStatisticsUseCase } from '@business-base/application/use-cases/negotiation-statistics.use-case';
import { NegotiationStatisticsResponseDto } from '@business-base/application/dto/out/negotiation-statistics-response.dto';
import { NegotiationStatisticsQueryParamsDto } from '@business-base/application/dto/in/negotiation-statistics-query-params.dto';
import { ExcludeGuards } from '../../../common/auth/decorators/exclude-guard.decorator';
import { AuthnGuard } from '../../../common/auth/authn.guard';
import { AuthzAccountGuard } from '../../../common/auth/authz-account.guard';
import { AuthzUserInAccountGuard } from '../../../common/auth/authz-user-in-account.guard';

@UserRolesInAccount(UserRoleInAccount.ADMIN)
@AccountRoles(AccountRole.BASIC)
@Controller('business-base/metrics')
export class MetricsController {
  constructor(
    private readonly metricUseCase: MetricUseCase,
    private readonly portfolioUseCase: PortfolioUseCase,
    private readonly negotiationStatisticsUseCase: NegotiationStatisticsUseCase,
    @Inject('PortfolioItemPort') private readonly portfolioItemAdapter: PortfolioItemPort,
    @Inject('PortfolioPort') private readonly portfolioAdapter: PortfolioPort,
  ) {}

  @Get('portfolio/created')
  @Version('1')
  async getPortfolioCreatedMetrics(
    @Query('startDate') startDate: Date,
    @Query('endDate') endDate: Date,
    @Req() request: Request,
  ) {
    const { customerId } = request['user'];

    const metrics = await this.metricUseCase.getPortfolioCreatedMetrics(
      customerId,
      startDate,
      endDate,
    );

    return {
      statusCode: 200,
      data: metrics,
    };
  }

  @Get('portfolio/items/created')
  @Version('1')
  async getPortfolioItemCreatedMetrics(
    @Query('startDate') startDate: Date,
    @Query('endDate') endDate: Date,
    @Req() request: Request,
  ) {
    const { customerId } = request['user'];

    const metrics = await this.metricUseCase.getPortfolioItemCreatedMetrics(
      customerId,
      startDate,
      endDate,
    );

    return {
      statusCode: 200,
      data: metrics,
    };
  }

  @Get('portfolio/items/with-interaction')
  @Version('1')
  async getPortfolioItemsWithInteractionCount(
    @Query('startDate') startDate: Date,
    @Query('endDate') endDate: Date,
    @Query('groupByDate') groupByDate: GroupByDate,
    @Req() request: Request,
  ) {
    const { customerId } = request['user'];

    const metrics = await this.metricUseCase.getPortfolioItemsWithInteractionCountByDate(
      customerId,
      startDate,
      endDate,
      groupByDate,
    );

    return {
      statusCode: 200,
      data: metrics,
    };
  }

  @ApiOperation({ summary: 'Get portfolio items with only AI interaction count' })
  @ApiQuery({
    name: 'startDate',
    type: String,
    format: 'date-time',
    required: true,
    example: '2024-01-01T00:00:00.000Z',
  })
  @ApiQuery({
    name: 'endDate',
    type: String,
    format: 'date-time',
    required: true,
    example: '2024-01-31T23:59:59.999Z',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved AI-only interaction count',
    schema: {
      example: {
        statusCode: 200,
        data: 42,
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @Get('portfolio/items/ai-only-interaction')
  @Version('1')
  async getPortfolioItemsWithAiOnlyInteractionCount(
    @Query('startDate') startDate: Date,
    @Query('endDate') endDate: Date,
    @Req() request: Request,
  ) {
    const { customerId } = request['user'];

    const metrics = await this.metricUseCase.getPortfolioItemsWithOnlyAiInteractionCountByDate(
      customerId,
      startDate,
      endDate,
    );

    return {
      statusCode: 200,
      data: metrics,
    };
  }

  @Get('portfolio/items/grouped-by-date')
  @Version('1')
  async getPortfolioItemsCountByDate(
    @Query('startDate') startDate: Date,
    @Query('endDate') endDate: Date,
    @Query('groupByDate') groupByDate: GroupByDate,
    @Query('currentStatus') currentStatus: PortfolioItemStatus,
    @Req() request: Request,
  ) {
    const { customerId } = request['user'];

    const metrics = await this.metricUseCase.getPortfolioItemsCountByDateFilteredByStatus(
      customerId,
      startDate,
      endDate,
      groupByDate,
      currentStatus,
    );

    return {
      statusCode: 200,
      data: metrics,
    };
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get total deal value across all portfolios' })
  @ApiQuery({
    name: 'startDate',
    type: String,
    format: 'date-time',
    required: false,
    example: '2024-01-01T00:00:00.000Z',
    description: 'Start date for deal value filtering',
  })
  @ApiQuery({
    name: 'endDate',
    type: String,
    format: 'date-time',
    required: false,
    example: '2024-12-31T23:59:59.999Z',
    description: 'End date for deal value filtering',
  })
  @ApiResponse({
    status: 200,
    description: 'Total deal value across all portfolios',
    schema: {
      example: {
        statusCode: 200,
        data: {
          totalDealValue: 1500000,
          startDate: '2024-01-01T00:00:00.000Z',
          endDate: '2024-12-31T23:59:59.999Z',
        },
      },
    },
  })
  @Get('portfolio/deal-value')
  @Version('1')
  async getTotalDealValue(
    @Req() request: Request,
    @Query('startDate') startDate?: Date,
    @Query('endDate') endDate?: Date,
  ) {
    const { customerId } = request['user'];

    const totalDealValue = await this.portfolioUseCase.getTotalDealValueByCustomer(
      customerId,
      startDate,
      endDate,
    );

    const formattedValue = new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(totalDealValue / 100);

    return {
      statusCode: 200,
      data: {
        totalDealValue: formattedValue,
        startDate: startDate ? new Date(startDate).toISOString() : undefined,
        endDate: endDate ? new Date(endDate).toISOString() : undefined,
      },
    };
  }

  @ApiOperation({ summary: 'Get average ticket for a portfolio' })
  @ApiQuery({
    name: 'startDate',
    type: String,
    format: 'date-time',
    required: false,
    example: '2024-01-01T00:00:00.000Z',
    description: 'Start date for deal value filtering',
  })
  @ApiQuery({
    name: 'endDate',
    type: String,
    format: 'date-time',
    required: false,
    example: '2024-12-31T23:59:59.999Z',
    description: 'End date for deal value filtering',
  })
  @ApiResponse({
    status: 200,
    description: 'Average ticket for a portfolio',
    schema: {
      example: {
        statusCode: 200,
        data: {
          averageTicket: 1500000,
          startDate: '2024-01-01T00:00:00.000Z',
          endDate: '2024-12-31T23:59:59.999Z',
        },
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Portfolio not found' })
  @ApiBearerAuth()
  @Get('portfolio/average-ticket/:portfolioId')
  @Version('1')
  async getAverageTicket(
    @Req() request: Request,
    @Param('portfolioId') portfolioId: string,
    @Query('startDate') startDate?: Date,
    @Query('endDate') endDate?: Date,
  ) {
    const totalDealValue = await this.portfolioUseCase.getTotalDealValueByPortfolioId(
      portfolioId,
      startDate,
      endDate,
    );

    const totalPortfolioItems = await this.portfolioItemAdapter.countSuccessfulByPortfolioId(
      portfolioId,
    );

    const averageTicket = totalDealValue / totalPortfolioItems;

    const formattedValue = new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(averageTicket / 100);

    return {
      statusCode: 200,
      data: {
        averageTicket: formattedValue,
        startDate: startDate ? new Date(startDate).toISOString() : undefined,
        endDate: endDate ? new Date(endDate).toISOString() : undefined,
      },
    };
  }

  @ApiOperation({ summary: 'Get customer average ticket across all portfolios' })
  @ApiQuery({
    name: 'startDate',
    type: String,
    format: 'date-time',
    required: false,
    example: '2024-01-01T00:00:00.000Z',
    description: 'Start date for deal value filtering',
  })
  @ApiQuery({
    name: 'endDate',
    type: String,
    format: 'date-time',
    required: false,
    example: '2024-12-31T23:59:59.999Z',
    description: 'End date for deal value filtering',
  })
  @ApiResponse({
    status: 200,
    description: 'Customer average ticket calculated successfully',
    schema: {
      example: {
        statusCode: 200,
        data: {
          averageTicket: 'R$ 1.250,75',
          startDate: '2024-01-01T00:00:00.000Z',
          endDate: '2024-12-31T23:59:59.999Z',
        },
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiBearerAuth()
  @ExcludeGuards(AuthnGuard.name, AuthzAccountGuard.name, AuthzUserInAccountGuard.name)
  @Get('customer/average-ticket')
  @Version('1')
  async getCustomerAverageTicket(
    @Req() request: Request,
    @Query('startDate') startDate?: Date,
    @Query('endDate') endDate?: Date,
  ) {
    // const { customerId } = request['user'];

    const averageTicket = await this.portfolioUseCase.getTotalDealValueByCustomer(
      'de642fea-ee38-43d9-9b2d-d0c2c4f329b9',
      startDate,
      endDate,
    );

    const formattedValue = new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(averageTicket / 100);

    return {
      statusCode: 200,
      data: {
        averageTicket: formattedValue,
        startDate: startDate ? new Date(startDate).toISOString() : undefined,
        endDate: endDate ? new Date(endDate).toISOString() : undefined,
      },
    };
  }

  @ApiOperation({
    summary: 'Get negotiation statistics for a specific portfolio',
    description:
      'Returns counts of active and in-progress negotiations. Active negotiations are those NOT in final states (FAILED, CANCELLED, OPTED_OUT, FINISHED), and in-progress negotiations are those with IN_PROGRESS status.',
  })
  @ApiQuery({
    name: 'startDate',
    type: String,
    format: 'date',
    required: true,
    example: '2024-01-01',
    description: 'Start date for filtering negotiations (YYYY-MM-DD format)',
  })
  @ApiQuery({
    name: 'endDate',
    type: String,
    format: 'date',
    required: true,
    example: '2024-12-31',
    description: 'End date for filtering negotiations (YYYY-MM-DD format)',
  })
  @ApiParam({
    name: 'portfolioId',
    type: String,
    required: true,
    example: '123e4567-e89b-12d3-a456-426614174000',
    description: 'Portfolio ID to filter negotiations',
  })
  @ApiResponse({
    status: 200,
    description: 'Negotiation statistics retrieved successfully',
    type: NegotiationStatisticsResponseDto,
    schema: {
      example: {
        statusCode: 200,
        data: {
          portfolios: [
            {
              portfolioId: '123e4567-e89b-12d3-a456-426614174000',
              activeNegotiations: 15,
              inProgressNegotiations: 8,
              totalNegotiations: 23,
            },
          ],
          summary: {
            totalActiveNegotiations: 15,
            totalInProgressNegotiations: 8,
            totalNegotiations: 23,
          },
        },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Bad Request - Invalid parameters' })
  @ApiResponse({ status: 404, description: 'Portfolio not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiBearerAuth()
  @Get('negotiations/:portfolioId')
  @Version('1')
  async getNegotiationStatistics(
    @Query() queryParams: NegotiationStatisticsQueryParamsDto,
    @Param('portfolioId', ParseUUIDPipe) portfolioId: string,
    @Req() request: Request,
  ): Promise<{ statusCode: number; data: NegotiationStatisticsResponseDto }> {
    const { customerId } = request['user'];
    const { startDate, endDate } = queryParams;

    const statistics = await this.negotiationStatisticsUseCase.getNegotiationStatistics(
      customerId,
      portfolioId,
      startDate,
      endDate,
    );

    return {
      statusCode: 200,
      data: statistics,
    };
  }
}
