import { logger } from '@edutalent/commons-sdk';
import { WhatsAppCommonObject } from '@message-hub/misc/interfaces/whatsapp-api/webhook-common';
import { Inject, Injectable } from '@nestjs/common';
import { CommunicationChannel, MessageType } from '@common/enums';
import { MessageUseCase } from '@message-hub/application/use-cases/message.use-case';
import { IncomingMessageRequestDto } from '@message-hub/application/dto/in/incoming-message-request.dto';
import { InfraWhatsAppApiPort } from '@message-hub/infrastructure/ports/http/whatsapp-api.port';
import { CustomerPhonePort } from '@message-hub/infrastructure/ports/db/customer-phone.port';
import { WhatsappApiService } from '@message-hub/domain/services/whatsapp-api.service';

@Injectable()
export class WhatsappApiWebhookUseCase {
  constructor(
    private readonly messageUseCase: MessageUseCase,
    @Inject('InfraWhatsAppApiPort')
    private readonly infraWhatsAppApiAdapter: InfraWhatsAppApiPort,
    @Inject('CustomerPhonePort')
    private readonly customerPhoneAdapter: CustomerPhonePort,
    private readonly whatsappApiService: WhatsappApiService,
  ) {}

  async handleWebhook(data: WhatsAppCommonObject) {
    try {
      logger.info(`Received webhook data START: ${JSON.stringify(data)}`);
      const { isIncomingMessage } = this.whatsappApiService.verifyIncomingMessage(data);

      if (!isIncomingMessage) {
        return;
      }
      await this.handleGeneralWebhook(data);
    } catch (error) {
      logger.error(
        `Error handle whatsapp webhook: Error message - ${error.message} - Data: ${JSON.stringify(
          data,
        )}`,
      );
    }
  }

  async handleGeneralWebhook(data: WhatsAppCommonObject) {
    logger.info(`Received webhook data GENERAL: ${JSON.stringify(data)}`);
    const to = data.entry[0]?.changes[0]?.value?.metadata?.display_phone_number;
    const customerPhoneEntity =
      await this.customerPhoneAdapter.getByPhoneNumberAndCommunicationChannel(
        to,
        CommunicationChannel.WHATSAPP_API,
      );
    logger.info(
      `Received webhook data CUSTOMER PHONE: ${JSON.stringify(customerPhoneEntity)} - TO: ${to}`,
    );

    if (!customerPhoneEntity) {
      throw new Error(`Customer phone not found for phone number id: ${to}`);
    }

    if (customerPhoneEntity.phoneNumber !== to) {
      throw new Error(
        `Phone number ID mismatch: received ${to}, expected ${customerPhoneEntity.phoneNumber}`,
      );
    }

    let incomingMessageRequestDto: IncomingMessageRequestDto;
    const messageType = data.entry[0]?.changes[0]?.value?.messages[0]?.type;
    const from = this.whatsappApiService.adjustContactNumber(
      data.entry[0]?.changes[0]?.value?.messages[0]?.from,
    );

    logger.info(`Received webhook data FROM: ${from} - MESSAGE TYPE: ${messageType} - TO: ${to}`);

    if (messageType.toUpperCase() === MessageType.TEXT) {
      incomingMessageRequestDto = {
        from,
        to,
        messageType: MessageType.TEXT,
        communicationChannel: CommunicationChannel.WHATSAPP_API,
        message: data.entry[0]?.changes[0]?.value?.messages[0]?.text?.body,
      };
    }

    if (messageType.toUpperCase() === MessageType.AUDIO) {
      const mediaId = data.entry[0]?.changes[0]?.value?.messages[0]?.audio?.id;
      const mediaBuffer = await this.infraWhatsAppApiAdapter.getMediaBuffer(
        customerPhoneEntity,
        mediaId,
      );

      incomingMessageRequestDto = {
        from,
        to,
        messageType: MessageType.AUDIO,
        communicationChannel: CommunicationChannel.WHATSAPP_API,
        fileBuffer: mediaBuffer,
      };
    }

    if (messageType.toUpperCase() === 'DOCUMENT') {
      const mediaId = data.entry[0]?.changes[0]?.value?.messages[0]?.document?.id;
      const mediaBuffer = await this.infraWhatsAppApiAdapter.getMediaBuffer(
        customerPhoneEntity,
        mediaId,
      );

      incomingMessageRequestDto = {
        from,
        to,
        messageType: MessageType.PDF,
        communicationChannel: CommunicationChannel.WHATSAPP_API,
        fileBuffer: mediaBuffer,
      };
    }
    logger.info(
      `Received webhook data INCOMING MESSAGE REQUEST DTO: ${JSON.stringify(
        incomingMessageRequestDto,
      )}`,
    );

    await this.messageUseCase.receiveMessage(incomingMessageRequestDto);
  }
}
