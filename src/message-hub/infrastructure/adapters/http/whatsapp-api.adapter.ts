import { logger } from '@edutalent/commons-sdk';
import { InfraWhatsAppApiPort } from '@message-hub/infrastructure/ports/http/whatsapp-api.port';
import { Inject, Injectable } from '@nestjs/common';
import { lastValueFrom } from 'rxjs';
import { handleHttpError } from '@common/utils/handle-http-error';
import { HttpService } from '@nestjs/axios';
import { DefaultOutgoingMessage } from '@message-hub/application/models/default-message.models';
import { MessageType } from '@common/enums';
import { CustomerPhoneEntity } from '@message-hub/domain/entities/customer-phone.entity';
import { S3Service } from '@common/s3/s3.service';
import { Readable } from 'stream';
import { lookup as mimeLookup } from 'mime-types';
import FormData from 'form-data';
import { WhatsappApiService } from '@message-hub/domain/services/whatsapp-api.service';
import { CustomerChannelIntegrationDataDefinitionPort } from '@common/auth/db/ports/customer-channel-integration-data-definition.port';

@Injectable()
export class InfraWhatsappApiAdapter implements InfraWhatsAppApiPort {
  constructor(
    private readonly httpService: HttpService,
    private readonly s3Service: S3Service,
    @Inject('CustomerChannelIntegrationDataDefinitionPort')
    private readonly customerChannelIntegrationDataDefinitionAdapter: CustomerChannelIntegrationDataDefinitionPort,
    private readonly whatsappApiService: WhatsappApiService,
  ) {}

  async sendMessage(
    customerPhoneEntity: CustomerPhoneEntity,
    messageOutgoing: DefaultOutgoingMessage,
  ): Promise<void> {
    const token = await this.getToken(customerPhoneEntity);

    const payload = await this.generatePayload(messageOutgoing, customerPhoneEntity, token);

    const url = customerPhoneEntity.apiUrl;

    const headers = {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    };

    logger.info(
      `InfraWhatsAppApiAdapter - sending message to whatsapp - url: ${url} - request body: ${JSON.stringify(
        payload,
      )}`,
    );

    try {
      const response = await lastValueFrom(this.httpService.post(url, payload, { headers }));

      logger.info(
        `InfraWhatsAppApiAdapter - sending message to whatsapp - response: ${JSON.stringify(
          response,
        )}`,
      );

      if (response.status !== 200) {
        logger.error(
          `InfraWhatsAppApiAdapter - Whatsapp error status code receveid - status: ${response.status} - statusText: ${response.statusText}`,
        );

        throw new Error(
          `InfraWhatsAppApiAdapter - Whatsapp error status code receveid - status: ${response.status} - statusText: ${response.statusText}`,
        );
      }
    } catch (error) {
      logger.error(
        `InfraWhatsAppApiAdapter - Error sending message to whatsapp: ${JSON.stringify(error)}`,
      );
      handleHttpError(error, 'Infra-WhatsApp-Api-adapter');
    }
  }

  async getMediaBuffer(customerPhoneEntity: CustomerPhoneEntity, mediaId: string): Promise<Buffer> {
    const token = await this.getToken(customerPhoneEntity);
    const fileUrl = await this.getMediaUrl(mediaId, token);

    const headers = {
      Authorization: `Bearer ${token}`,
    };

    try {
      const fileResponse = await lastValueFrom(
        this.httpService.get(fileUrl, {
          responseType: 'arraybuffer',
          headers,
        }),
      );

      if (fileResponse.status !== 200) {
        logger.error(
          `InfraWhatsAppApiAdapter - Get audio buffer - Whatsapp error status code receveid - status: ${fileResponse.status} - statusText: ${fileResponse.statusText}`,
        );

        throw new Error(
          `InfraWhatsAppApiAdapter - Get audio buffer - Whatsapp error status code receveid - status: ${fileResponse.status} - statusText: ${fileResponse.statusText}`,
        );
      }

      return fileResponse.data;
    } catch (error) {
      logger.error(
        `InfraWhatsAppApiAdapter - Get audio buffer - Error getting audio buffer: ${JSON.stringify(
          error,
        )}`,
      );
      handleHttpError(error, 'Infra-WhatsApp-Api-adapter');
    }
  }

  private async getMediaUrl(mediaId: string, token: string): Promise<string> {
    const headers = {
      Authorization: `Bearer ${token}`,
    };

    try {
      const response = await lastValueFrom(
        this.httpService.get(
          `https://graph.facebook.com/${process.env.WHATSAPP_API_VERSION}/${mediaId}/`,
          {
            headers,
            timeout: 20000,
          },
        ),
      );

      if (response.status !== 200) {
        logger.error(
          `InfraWhatsAppApiAdapter - Get media url - Whatsapp error status code receveid - status: ${response.status} - statusText: ${response.statusText}`,
        );

        throw new Error(
          `InfraWhatsAppApiAdapter - Get media url - Whatsapp error status code receveid - status: ${response.status} - statusText: ${response.statusText}`,
        );
      }

      const fileUrl = response.data.url;

      return fileUrl;
    } catch (error) {
      logger.error(
        `InfraWhatsAppApiAdapter - Get media url - Error getting media url: ${JSON.stringify(
          error,
        )}`,
      );
      handleHttpError(error, 'Infra-WhatsApp-Api-adapter');
    }
  }

  private async generatePayload(
    messageOutgoing: DefaultOutgoingMessage,
    customerPhoneEntity: CustomerPhoneEntity,
    token: string,
  ): Promise<any> {
    if (messageOutgoing.messageType === MessageType.TEXT) {
      return {
        messaging_product: 'whatsapp',
        recipient_type: 'individual',
        to: messageOutgoing.to,
        type: 'text',
        text: {
          body: messageOutgoing.message,
        },
      };
    }

    if (messageOutgoing.messageType === MessageType.AUDIO) {
      const { mediaId } = await this.getMediaInfo(
        messageOutgoing.fileUrl,
        customerPhoneEntity,
        token,
      );

      return {
        messaging_product: 'whatsapp',
        recipient_type: 'individual',
        to: messageOutgoing.to,
        type: 'audio',
        audio: {
          id: mediaId,
        },
      };
    }

    if (messageOutgoing.messageType === MessageType.PDF) {
      const { mediaId, fileName } = await this.getMediaInfo(
        messageOutgoing.fileUrl,
        customerPhoneEntity,
        token,
      );

      return {
        messaging_product: 'whatsapp',
        recipient_type: 'individual',
        to: messageOutgoing.to,
        type: 'document',
        document: {
          id: mediaId,
          filename: fileName,
          caption: messageOutgoing.message ?? '',
        },
      };
    }
  }

  private async getToken(customerPhoneEntity: CustomerPhoneEntity): Promise<string> {
    const customerChannelIntegrationDataDefinition =
      await this.customerChannelIntegrationDataDefinitionAdapter.get(
        customerPhoneEntity.customerId,
      );

    const token = customerChannelIntegrationDataDefinition?.data?.token;

    if (!token) {
      throw new Error(
        `Token not found for customer phone number: ${customerPhoneEntity.customerId}`,
      );
    }

    return token;
  }

  private async getMediaInfo(
    fileUrl: string,
    customerPhoneEntity: CustomerPhoneEntity,
    token: string,
  ): Promise<{ mediaId: string; fileName: string }> {
    const fileStream = await this.s3Service.getFileStreamFromPath(fileUrl);

    if (!fileStream) {
      throw new Error(
        `File stream not found for file: ${fileUrl} - customerPhoneEntity: ${JSON.stringify(
          customerPhoneEntity,
        )}`,
      );
    }

    const fileName = fileUrl.split('/').pop();
    const { mediaId } = await this.updaloadAndGetMidiaId(
      fileStream,
      customerPhoneEntity,
      token,
      fileName,
    );

    return {
      mediaId,
      fileName,
    };
  }

  private async updaloadAndGetMidiaId(
    fileStream: Readable,
    customerPhoneEntity: CustomerPhoneEntity,
    token: string,
    fileName: string,
  ): Promise<{ mediaId: string }> {
    const phoneNumberId = this.whatsappApiService.extractPhoneNumberIdFromUrl(
      customerPhoneEntity.apiUrl,
    );

    if (!phoneNumberId) {
      throw new Error(
        `Phone number id not found for customer phone entity: ${JSON.stringify(
          customerPhoneEntity,
        )}`,
      );
    }

    const url = `https://graph.facebook.com/${process.env.WHATSAPP_API_VERSION}/${phoneNumberId}/media`;

    const form = new FormData();

    const contentType = mimeLookup(fileName);

    if (!contentType) {
      throw new Error(
        `Content type not found for file: ${fileName} - customerPhoneEntity: ${JSON.stringify(
          customerPhoneEntity,
        )}`,
      );
    }

    if (contentType !== 'application/pdf' || contentType !== 'audio/mpeg') {
      throw new Error(
        `Content type not supported for file: ${fileName} - customerPhoneEntity: ${JSON.stringify(
          customerPhoneEntity,
        )}`,
      );
    }

    form.append('file', fileStream, {
      filename: fileName,
      contentType,
    });
    form.append('messaging_product', 'whatsapp');
    form.append('type', contentType);

    const headers = {
      ...form.getHeaders(),
      Authorization: `Bearer ${token}`,
    };

    try {
      const response = await lastValueFrom(this.httpService.post(url, form, { headers }));

      if (response.status !== 200) {
        logger.error(
          `InfraWhatsAppApiAdapter - Upload media - Whatsapp error - status: ${response.status} - statusText: ${response.statusText}`,
        );
        throw new Error(
          `InfraWhatsAppApiAdapter - Upload media - Whatsapp error - status: ${response.status} - statusText: ${response.statusText}`,
        );
      }

      return {
        mediaId: response.data.id,
      };
    } catch (error) {
      logger.error(`InfraWhatsAppApiAdapter - Upload media failed`, error);
      throw error;
    }
  }
}
